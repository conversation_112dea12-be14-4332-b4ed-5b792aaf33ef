# Dependencies
node_modules/
.pnp/
.pnp.js

# Build outputs
dist/
build/
*.tsbuildinfo
.turbo/

# Testing
coverage/
.nyc_output/

# Environment files
.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.history/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
debug.log
logs/
*.log

# Memory storage (from your example code)
.memory/
temp-examples/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache
adk-python/*
apps/examples/src/sqlite-runner-example/data/*

# PgLite
apps/examples/src/pglite-runner-example/data/*

# To ignore all lock files that are not pnpm
# This is useful if you want to ensure that only pnpm lock files are present in the repository
package-lock.json
yarn.lock