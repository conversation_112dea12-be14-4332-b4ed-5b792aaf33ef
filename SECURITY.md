# Security Policy

## Reporting a Vulnerability

We take the security seriously. If you believe you've found a security vulnerability, please report it by creating a GitHub issue. For sensitive reports, please contact our admins in telegram [@IQAICOM](https://t.me/IQAICOM).

### How to Report

1. Go to the [Issues](https://github.com/IQAIcom/brain/issues) section of the repository
2. Click "New Issue"
3. Select "Security Vulnerability" (if available) or create a regular issue
4. Add the label "security" to your issue
5. Provide a clear description of the vulnerability

### What to Include

When reporting a security issue, please include:

- A clear description of the vulnerability
- Steps to reproduce the issue
- Potential impact of the vulnerability
- Suggestions for addressing the issue (if available)
- Any related code snippets or screenshots
- Environment details (OS, Node.js version, etc.)

### What to Expect

After you submit a security issue:

1. We will acknowledge receipt of your report as soon as possible
2. We will provide an initial assessment of the report's validity and severity
3. We will keep you informed about our progress in addressing the issue
4. Once resolved, we will credit you in our security acknowledgments (unless you prefer to remain anonymous)

## Security Best Practices

When using Brain Framework:

1. Keep all dependencies up to date
2. Use proper authentication for any agent deployments
3. Be cautious when using plugins that interface with external systems
4. Review your application's security regularly
5. Follow established security practices for any environments where agents are deployed

## Scope

This security policy applies to the latest version of Brain Framework and its core plugins. Third-party plugins or modified versions of the framework may have different security considerations.

## Security Updates

Security updates will be released as part of our regular release cycle or as emergency patches for critical vulnerabilities. We recommend always using the latest version of Brain Framework and its dependencies.

Thank you for helping keep Brain Framework and its community safe!
