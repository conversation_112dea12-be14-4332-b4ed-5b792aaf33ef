@import 'tailwindcss';
@import 'fumadocs-ui/css/shadcn.css';
@import 'fumadocs-ui/css/preset.css';

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.2077 0.0398 265.7549);
  --card: oklch(0.9846 0.0017 247.8389);
  --card-foreground: oklch(0.2077 0.0398 265.7549);
  --popover: oklch(0.9769 0.0011 17.1781);
  --popover-foreground: oklch(0.2077 0.0398 265.7549);
  --primary: oklch(0.7103 0.2100 354.2024);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9276 0.0058 264.5313);
  --secondary-foreground: oklch(0.2077 0.0398 265.7549);
  --muted: oklch(0.9670 0.0029 264.5419);
  --muted-foreground: oklch(0.4455 0.0374 257.2808);
  --accent: oklch(0.8717 0.0093 258.3382);
  --accent-foreground: oklch(0.2077 0.0398 265.7549);
  --destructive: oklch(0.6358 0.2088 25.4126);
  --destructive-foreground: oklch(0.9851 0 0);
  --border: oklch(0.9268 0.0063 255.4767);
  --input: oklch(0.8717 0.0093 258.3382);
  --ring: oklch(0.8021 0.1343 350.3252);
  --chart-1: oklch(0.6767 0.1572 35.0959);
  --chart-2: oklch(0.6307 0.1009 183.8484);
  --chart-3: oklch(0.3790 0.0438 226.1538);
  --chart-4: oklch(0.8330 0.1185 88.3461);
  --chart-5: oklch(0.7843 0.1256 58.9964);
  --sidebar: oklch(0.9881 0 0);
  --sidebar-foreground: oklch(0.2645 0 0);
  --sidebar-primary: oklch(0.3250 0 0);
  --sidebar-primary-foreground: oklch(0.9881 0 0);
  --sidebar-accent: oklch(0.9761 0 0);
  --sidebar-accent-foreground: oklch(0.3250 0 0);
  --sidebar-border: oklch(0.9401 0 0);
  --sidebar-ring: oklch(0.7731 0 0);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2401 0.0249 253.7688);
  --foreground: oklch(0.9884 0.0057 128.5242);
  --card: oklch(0.2574 0.0262 255.7576);
  --card-foreground: oklch(0.9884 0.0057 128.5242);
  --popover: oklch(0.2816 0.0279 255.1758);
  --popover-foreground: oklch(0.9884 0.0057 128.5242);
  --primary: oklch(0.6532 0.2555 0.6048);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.2816 0.0279 255.1758);
  --secondary-foreground: oklch(0.9884 0.0057 128.5242);
  --muted: oklch(0.2961 0.0218 262.5183);
  --muted-foreground: oklch(0.8638 0 0);
  --accent: oklch(0.2101 0.0318 264.6645);
  --accent-foreground: oklch(0.9884 0.0057 128.5242);
  --destructive: oklch(0.5588 0.1629 36.6381);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3719 0.0202 254.0850);
  --input: oklch(0.4502 0.0172 254.7110);
  --ring: oklch(0.7106 0.2105 353.8284);
  --chart-1: oklch(0.5298 0.1932 262.0493);
  --chart-2: oklch(0.6994 0.1339 165.4606);
  --chart-3: oklch(0.7227 0.1502 60.5799);
  --chart-4: oklch(0.6193 0.2029 312.7422);
  --chart-5: oklch(0.6118 0.2093 6.1387);
  --sidebar: oklch(0.2103 0.0059 285.8852);
  --sidebar-foreground: oklch(0.9674 0.0013 286.3752);
  --sidebar-primary: oklch(0.4882 0.2172 264.3763);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.2739 0.0055 286.0326);
  --sidebar-accent-foreground: oklch(0.9674 0.0013 286.3752);
  --sidebar-border: oklch(0.2739 0.0055 286.0326);
  --sidebar-ring: oklch(0.8711 0.0055 286.2860);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}