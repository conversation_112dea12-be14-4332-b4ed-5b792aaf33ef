---
title: Agents
description: Build AI agents that can reason, use tools, and coordinate with other agents
---

import { Cards, Card } from 'fumadocs-ui/components/card';
import { Callout } from 'fumadocs-ui/components/callout';

Agents are the core abstraction in ADK TypeScript. They encapsulate AI models, instructions, tools, and coordination logic to create autonomous programs that can understand instructions and complete complex tasks.

## Agent Types

ADK TypeScript provides different types of agents for different use cases:

<Cards>
  <Card
    title="🤖 LLM Agents"
    description="Use language models for reasoning, decision-making, and tool usage"
    href="/docs/framework/agents/llm-agents"
  />

  <Card
    title="🔗 Sequential Agents"
    description="Execute sub-agents in order for predictable workflows"
    href="/docs/framework/agents/workflow-agents/sequential-agents"
  />

  <Card
    title="⚡ Parallel Agents"
    description="Run multiple agents simultaneously for faster processing"
    href="/docs/framework/agents/workflow-agents/parallel-agents"
  />

  <Card
    title="🔄 Loop Agents"
    description="Repeat agent execution until conditions are met"
    href="/docs/framework/agents/workflow-agents/loop-agents"
  />

  <Card
    title="🏗️ Custom Agents"
    description="Build specialized agents with custom logic and behavior"
    href="/docs/framework/agents/custom-agents"
  />

  <Card
    title="👥 Multi-Agent Systems"
    description="Coordinate multiple agents for complex distributed tasks"
    href="/docs/framework/agents/multi-agents"
  />
</Cards>

## Models & Configuration

<Cards>
  <Card
    title="🧠 Model Support"
    description="Configure and use different LLM models with your agents"
    href="/docs/framework/agents/models"
  />
</Cards>

## Choosing the Right Agent Type

<Callout type="info" title="Agent Selection Guide">
- **LLM Agents**: Best for reasoning, conversation, and tool usage
- **Sequential Agents**: Use for step-by-step workflows and pipelines
- **Parallel Agents**: Ideal for independent tasks that can run simultaneously
- **Loop Agents**: Perfect for iterative improvement and retry logic
- **Custom Agents**: When you need specific behavior or integration patterns
- **Multi-Agent**: For complex coordination and specialized task distribution
</Callout>

## Getting Started

New to agents? Start with these foundational concepts:

1. **[LLM Agents](/docs/framework/agents/llm-agents)** - Learn the most common agent type
2. **[Workflow Agents](/docs/framework/agents/workflow-agents)** - Understand orchestration patterns
3. **[Models](/docs/framework/agents/models)** - Configure LLM models and settings

## Advanced Topics

Once you're comfortable with the basics:

- **[Custom Agents](/docs/framework/agents/custom-agents)** - Build specialized agent types
- **[Multi-Agent Systems](/docs/framework/agents/multi-agents)** - Coordinate multiple agents