---
title: Multi-Agent Systems
description: Coordinate multiple agents for complex distributed tasks
---

import { <PERSON><PERSON>, Card } from 'fumadocs-ui/components/card';
import { Callout } from 'fumadocs-ui/components/callout';

Multi-Agent Systems (MAS) in ADK TypeScript enable you to build sophisticated applications by composing multiple specialized agents. Instead of creating one monolithic agent, you can structure your application as a hierarchy of collaborating agents.

## Why Multi-Agent Systems?

As agentic applications grow in complexity, multi-agent architectures provide significant advantages:

- **Modularity**: Break complex problems into manageable, specialized components
- **Reusability**: Use specialized agents across different workflows and applications
- **Maintainability**: Easier to update and debug individual agent components
- **Scalability**: Distribute workload across multiple agents and systems
- **Specialization**: Create agents with focused expertise and capabilities

<Callout type="info" title="Architectural Benefits">
Multi-agent systems make complex applications more manageable by creating clear separation of concerns and enabling modular development.
</Callout>

## Agent Composition Types

You can compose various types of agents to build multi-agent systems:

<Cards>
  <Card
    title="🤖 LLM Agents"
    description="Reasoning and decision-making agents powered by language models"
    href="/docs/framework/agents/llm-agents"
  />

  <Card
    title="🔄 Workflow Agents"
    description="Orchestration agents that manage execution flow of sub-agents"
    href="/docs/framework/agents/workflow-agents"
  />

  <Card
    title="🏗️ Custom Agents"
    description="Specialized agents with custom logic and behavior"
    href="/docs/framework/agents/custom-agents"
  />
</Cards>

## Core Primitives

### Agent Hierarchy

The foundation of multi-agent systems is the parent-child relationship:

- **Tree Structure**: Agents form hierarchical relationships through `sub_agents`
- **Single Parent**: Each agent can only have one parent agent
- **Navigation**: Use `parent_agent` and `find_agent()` to traverse the hierarchy
- **Scope Definition**: Hierarchy defines delegation scope and workflow boundaries

### Workflow Orchestration

Specialized orchestration agents manage how sub-agents execute:

- **Sequential**: Execute agents one after another in order
- **Parallel**: Run multiple agents simultaneously for faster processing
- **Loop**: Repeat agent execution until conditions are met

### Communication Mechanisms

Agents coordinate through multiple communication patterns:

<Cards>
  <Card
    title="📊 Shared State"
    description="Asynchronous data sharing through session state"
  />

  <Card
    title="🎯 LLM-Driven Delegation"
    description="Dynamic routing based on LLM decision making"
  />

  <Card
    title="🔧 Explicit Invocation"
    description="Direct agent calls using AgentTool integration"
  />
</Cards>

## Communication Patterns

### Shared Session State

The most fundamental communication mechanism:

- **Passive Communication**: Agents read and write to shared session state
- **Data Flow**: Use `output_key` to automatically save agent results
- **State Management**: Track changes through callback mechanisms
- **Pipeline Coordination**: Perfect for sequential workflows and data processing

### LLM-Driven Delegation

Dynamic agent routing based on natural language understanding:

- **Intelligent Routing**: LLM agents choose appropriate sub-agents for tasks
- **Transfer Function**: Agents use `transfer_to_agent()` to delegate work
- **Context Switching**: Framework handles execution context changes
- **Flexible Coordination**: Adapts to varying inputs and requirements

### Explicit Invocation

Treat agents as tools for controlled interaction:

- **AgentTool Integration**: Wrap agents as callable tools
- **Synchronous Calls**: Explicit agent invocation within workflow
- **Result Capture**: Automatic handling of agent responses and state changes
- **Controlled Execution**: Precise control over agent interaction timing

## Design Patterns

### Hierarchical Delegation

Create coordinator agents that delegate to specialized sub-agents based on task requirements.

### Pipeline Processing

Build sequential workflows where each agent processes and transforms data for the next agent.

### Parallel Processing

Use parallel agents to handle independent tasks simultaneously for improved performance.

### Iterative Refinement

Implement loop-based workflows for progressive improvement and quality assurance.

## Best Practices

<Callout type="warn" title="Design Considerations">
- Keep agent responsibilities focused and well-defined
- Design clear interfaces between agents
- Plan data flow and state management carefully
- Implement proper error handling and recovery mechanisms
</Callout>

### Agent Design

- **Single Responsibility**: Each agent should have a clear, focused purpose
- **Clear Interfaces**: Design predictable input/output patterns
- **Descriptive Names**: Use names that clearly indicate agent capabilities
- **Minimal Dependencies**: Reduce coupling between agents

### State Management

- **Consistent Keys**: Use standard naming conventions for state keys
- **Data Validation**: Validate state data before processing
- **State Cleanup**: Clean up temporary state to prevent memory leaks
- **Conflict Prevention**: Avoid state key conflicts in parallel execution

### Error Handling

- **Graceful Degradation**: Handle sub-agent failures appropriately
- **Recovery Strategies**: Implement retry and fallback mechanisms
- **Error Propagation**: Decide which errors to handle vs. propagate
- **Monitoring**: Track agent health and performance metrics

## Performance Considerations

### Resource Management

- **Concurrent Limits**: Control the number of simultaneously executing agents
- **Memory Usage**: Monitor memory consumption in complex hierarchies
- **Network Resources**: Manage external API calls and rate limits
- **Timeout Handling**: Implement appropriate timeouts for agent operations

### Optimization Strategies

- **Agent Pooling**: Reuse agent instances when possible
- **Lazy Loading**: Load agents only when needed
- **Caching**: Cache frequently used agent results
- **Load Balancing**: Distribute work across available resources

## Testing Multi-Agent Systems

### Unit Testing

- **Individual Agents**: Test each agent in isolation
- **Mock Dependencies**: Use mocks for sub-agents and external services
- **State Testing**: Verify state management logic
- **Error Scenarios**: Test failure modes and recovery

### Integration Testing

- **End-to-End Workflows**: Test complete multi-agent scenarios
- **Communication Testing**: Verify agent interaction patterns
- **State Flow Testing**: Validate data flow between agents
- **Performance Testing**: Measure system performance under load

### Monitoring and Observability

- **Agent Tracing**: Track execution flow through agent hierarchy
- **Performance Metrics**: Monitor execution times and resource usage
- **Error Tracking**: Log and analyze agent failures
- **State Monitoring**: Track state changes and data flow

## Related Topics

<Cards>
  <Card
    title="💾 Sessions"
    description="Manage state and context across multi-agent workflows"
    href="/docs/framework/sessions"
  />

  <Card
    title="🔄 Workflow Agents"
    description="Learn about orchestration patterns"
    href="/docs/framework/agents/workflow-agents"
  />

  <Card
    title="📞 Callbacks"
    description="Monitor and control multi-agent execution"
    href="/docs/framework/callbacks"
  />

  <Card
    title="📊 Examples"
    description="See working multi-agent implementations"
    href="https://github.com/IQAIcom/adk-ts/tree/main/apps/examples"
    target="_blank"
    rel="noopener noreferrer"
  />
</Cards>