---
title: Workflow Agents
description: Orchestrate multiple agents with predictable execution patterns
---

import { <PERSON><PERSON>, Card } from 'fumadocs-ui/components/card';
import { Callout } from 'fumadocs-ui/components/callout';

Workflow agents provide deterministic orchestration patterns for coordinating multiple agents. Unlike LLM agents that make dynamic decisions, workflow agents follow predictable execution paths.

## Workflow Types

<Cards>
  <Card
    title="🔗 Sequential Agent"
    description="Execute agents one after another in a specific order"
    href="/docs/framework/agents/workflow-agents/sequential-agents"
  />

  <Card
    title="⚡ Parallel Agent"
    description="Run multiple agents simultaneously for faster processing"
    href="/docs/framework/agents/workflow-agents/parallel-agents"
  />

  <Card
    title="🔄 Loop Agent"
    description="Repeat agent execution until conditions are met"
    href="/docs/framework/agents/workflow-agents/loop-agents"
  />
</Cards>

## When to Use Workflow Agents

Workflow agents are ideal for:

- **Predictable pipelines** - When you need guaranteed execution order
- **Data processing workflows** - Multi-step data transformation and analysis
- **Quality assurance** - Review and validation workflows
- **Performance optimization** - Parallel processing of independent tasks
- **Iterative improvement** - Repeated refinement until criteria are met

## Key Benefits

### Deterministic Execution
Unlike LLM agents, workflow agents provide predictable, repeatable execution patterns.

### Composition
Combine different workflow types to create complex orchestration patterns.

### Error Handling
Built-in error handling and recovery mechanisms for robust workflows.

### Performance Control
Optimize execution with parallel processing and conditional loops.

## Workflow Patterns

<Callout type="info" title="Common Patterns">
- **Research Pipeline**: Sequential research → analysis → summary
- **Content Review**: Parallel fact-checking and grammar review
- **Iterative Refinement**: Loop until quality criteria are met
- **Distributed Processing**: Parallel execution across data partitions
</Callout>

## Choosing the Right Workflow

- **Sequential**: When order matters and each step depends on the previous
- **Parallel**: When tasks are independent and can run simultaneously
- **Loop**: When you need iterative improvement or retry logic

## Advanced Features

### Nested Workflows
Workflow agents can contain other workflow agents for complex patterns.

### Conditional Execution
Control flow based on agent outputs and session state.

### Result Aggregation
Combine results from multiple agents into coherent outputs.

### State Management
Pass data between agents using session state and context.

## Getting Started

Start with the workflow type that matches your use case:

1. **[Sequential Agents](/docs/agents/workflow-agents/sequential-agents)** - For step-by-step processes
2. **[Parallel Agents](/docs/agents/workflow-agents/parallel-agents)** - For independent tasks
3. **[Loop Agents](/docs/agents/workflow-agents/loop-agents)** - For iterative processes