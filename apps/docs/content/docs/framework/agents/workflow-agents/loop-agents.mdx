---
title: Loop Agents
description: Repeat agent execution until conditions are met
---

import { Callout } from 'fumadocs-ui/components/callout';
import { <PERSON><PERSON>, Card } from 'fumadocs-ui/components/card';

Loop agents repeatedly execute a single sub-agent until a stopping condition is met. This enables iterative improvement, retry logic, and progressive refinement workflows.

## How Loop Agents Work

1. **Iterative Execution**: Run the same agent multiple times
2. **Condition Checking**: Evaluate stopping conditions after each iteration
3. **Context Accumulation**: Build upon previous results in each iteration
4. **Controlled Termination**: Stop when conditions are met or limits are reached

## When to Use Loop Agents

Loop agents are perfect for:

- **Iterative improvement** - Refining content until quality criteria are met
- **Retry logic** - Attempting tasks until successful completion
- **Progressive research** - Deepening understanding through multiple iterations
- **Quality assurance** - Repeated validation until standards are achieved

<Callout type="info" title="Convergence Required">
Use loop agents when you expect the process to converge toward a solution or when you have clear stopping criteria.
</Callout>

## Configuration

### Basic Setup
- **agent**: The agent to execute repeatedly
- **name**: Unique identifier for the loop workflow
- **description**: Summary for other agents to understand the workflow

### Loop Control
- **max_iterations**: Maximum number of loop cycles (required)
- **stop_condition**: Function or condition to determine when to stop
- **iteration_limit**: Alternative way to set maximum iterations

### Advanced Options
- **accumulate_context**: Whether to build conversation history across iterations
- **timeout**: Maximum time for the entire loop execution
- **on_iteration**: Callback function to execute after each iteration

## Common Patterns

### Content Refinement
Repeatedly improve written content until it meets quality standards.

### Research Deepening
Iteratively explore topics to build comprehensive understanding.

### Problem Solving
Keep attempting different approaches until a solution is found.

### Validation Loops
Repeatedly check and fix issues until validation passes.

## Stopping Conditions

### Built-in Conditions
- **Maximum iterations**: Stop after a specified number of cycles
- **Timeout**: Stop after a maximum time period
- **Success detection**: Stop when the agent indicates completion

### Custom Conditions
- **Quality thresholds**: Stop when output quality meets criteria
- **State validation**: Stop when session state reaches target values
- **External triggers**: Stop based on external system conditions

## Best Practices

<Callout type="warn" title="Loop Safety">
- Always set reasonable maximum iteration limits to prevent infinite loops
- Design clear stopping conditions to ensure termination
- Monitor resource usage during extended loop execution
- Consider timeout mechanisms for time-sensitive operations
</Callout>

### Iteration Design
- Ensure each iteration makes meaningful progress
- Design the agent to build upon previous results
- Avoid repetitive behavior that doesn't add value
- Plan for diminishing returns in later iterations

### Stopping Strategy
- Define clear, measurable stopping criteria
- Use multiple stopping conditions for robustness
- Test stopping conditions thoroughly
- Consider edge cases and failure modes

### Performance Considerations
- Balance iteration thoroughness with execution time
- Monitor resource consumption during loops
- Consider breaking long loops into smaller chunks
- Plan for graceful degradation on timeout

## Monitoring and Control

### Progress Tracking
- Log iteration progress and results
- Monitor convergence toward stopping conditions
- Track resource usage across iterations
- Measure improvement between iterations

### Error Handling
- Handle agent failures within iterations
- Implement retry logic for transient errors
- Consider partial results when loops terminate early
- Plan for recovery from iteration failures

## Related Topics

<Cards>
  <Card
    title="🔗 Sequential Agents"
    description="Execute agents in order for pipeline workflows"
    href="/docs/framework/agents/workflow-agents/sequential-agents"
  />

  <Card
    title="⚡ Parallel Agents"
    description="Run multiple agents simultaneously"
    href="/docs/framework/agents/workflow-agents/parallel-agents"
  />

  <Card
    title="📊 Monitoring"
    description="Monitor and observe agent execution"
    href="/docs/framework/advanced/monitoring"
  />
</Cards>