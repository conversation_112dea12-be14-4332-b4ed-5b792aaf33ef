---
title: Event Actions
description: Understanding the EventActions system for state management and control flow
---

import { Callout } from 'fumadocs-ui/components/callout';
import { Tabs, Tab } from 'fumadocs-ui/components/tabs';

Event Actions are the mechanism by which ADK components signal side effects, state changes, and control flow instructions. They enable powerful features like state management, agent transfers, and artifact tracking.

## What Event Actions Are

The `EventActions` class represents the actions attached to an event. It contains instructions for:

- **State Changes**: Updates to session state via `stateDelta`
- **Artifact Management**: Tracking file versions via `artifactDelta`
- **Control Flow**: Agent transfers and escalation signals
- **Tool Behavior**: Skipping summarization and authentication requests

## EventActions Structure

```typescript
import { EventActions } from '@iqai/adk';

class EventActions {
  // Control tool response processing
  skipSummarization?: boolean;

  // State management
  stateDelta: Record<string, any> = {};

  // Artifact versioning
  artifactDelta: Record<string, number> = {};

  // Flow control
  transferToAgent?: string;
  escalate?: boolean;

  // Authentication
  requestedAuthConfigs?: Record<string, any>;
}
```

## Action Types

### State Management

State deltas track changes to session state:

<Tabs items={['Usage', 'Example']}>
<Tab value="Usage">
```typescript
const actions = new EventActions({
  stateDelta: {
    'user_preference': 'dark_mode',
    'last_activity': Date.now(),
    'temp_calculation': 42  // Will be filtered out (temp_ prefix)
  }
});

const event = new Event({
  author: 'MyAgent',
  actions: actions,
  content: { parts: [{ text: 'Updated preferences' }] }
});
```
</Tab>

<Tab value="Example">
```typescript
// In a tool or agent callback
context.state['user_preference'] = 'dark_mode';
context.state['app:global_setting'] = 'enabled';
context.state['temp_intermediate_result'] = calculation;

// These changes automatically populate the next event's stateDelta
```
</Tab>
</Tabs>

<Callout type="info">
State keys with `temp_` prefix are excluded from persistence to avoid cluttering permanent session state.
</Callout>

### Artifact Management

Artifact deltas track file saves and versions:

```typescript
const actions = new EventActions({
  artifactDelta: {
    'report.pdf': 1,
    'analysis.json': 3,
    'diagram.png': 1
  }
});
```

### Control Flow Actions

#### Agent Transfer

Transfer control to another agent:

```typescript
const actions = new EventActions({
  transferToAgent: 'BillingAgent'
});

// The framework will route the next turn to BillingAgent
```

#### Escalation

Signal that a loop or process should terminate:

```typescript
const actions = new EventActions({
  escalate: true
});

// Useful in loop agents or error handling
```

### Tool Behavior Control

#### Skip Summarization

Prevent the LLM from summarizing tool results:

```typescript
const actions = new EventActions({
  skipSummarization: true
});

// Tool result will be displayed directly without LLM processing
```

#### Authentication Requests

Signal that tools need authentication:

```typescript
const actions = new EventActions({
  requestedAuthConfigs: {
    'gmail-tool': {
      type: 'oauth',
      provider: 'gmail'
    }
  }
});
```

## Working with Actions in Events

### Detecting Actions

Check for actions in received events:

```typescript
for await (const event of runner.runAsync(query, session)) {
  if (event.actions) {
    // Check for state changes
    if (Object.keys(event.actions.stateDelta).length > 0) {
      console.log('State updated:', event.actions.stateDelta);
    }

    // Check for artifact updates
    if (Object.keys(event.actions.artifactDelta).length > 0) {
      console.log('Artifacts saved:', event.actions.artifactDelta);
    }

    // Check for control signals
    if (event.actions.transferToAgent) {
      console.log(`Transferring to: ${event.actions.transferToAgent}`);
    }

    if (event.actions.escalate) {
      console.log('Escalation signal received');
    }
  }
}
```

### Creating Events with Actions

When building custom agents:

```typescript
import { Event, EventActions } from '@iqai/adk';

export class CustomAgent extends BaseAgent {
  async *runAsyncImpl(context: InvocationContext): AsyncGenerator<Event> {
    // Perform some logic...

    const actions = new EventActions({
      stateDelta: {
        'task_status': 'completed',
        'result_count': 5
      },
      transferToAgent: 'ReviewAgent'
    });

    yield new Event({
      author: this.name,
      invocationId: context.invocationId,
      content: {
        parts: [{ text: 'Task completed, transferring for review' }]
      },
      actions: actions
    });
  }
}
```

## State Scope Patterns

Use prefixes to indicate state scope:

- **`app:`** - Application-wide settings
- **`user:`** - User-specific preferences across sessions
- **`temp_`** - Temporary values (filtered from persistence)
- **No prefix** - Session-specific state

```typescript
context.state['app:theme'] = 'dark';           // App setting
context.state['user:language'] = 'en';         // User preference
context.state['temp_calculation'] = result;    // Temporary (not persisted)
context.state['current_task'] = 'processing';  // Session state
```

## Best Practices

### Action Design

- **Use specific actions**: Be explicit about what each action signals
- **Combine logically**: Group related state changes in one event
- **Document side effects**: Clear comments for complex action combinations

### State Management

- **Minimize state**: Only store what's needed across turns
- **Use appropriate prefixes**: Indicate scope and persistence intent
- **Avoid large objects**: Keep state values reasonably sized

### Control Flow

- **Single responsibility**: One control action per event when possible
- **Clear transfers**: Use descriptive agent names for transfers
- **Document escalation**: Explain why escalation occurs

Event Actions provide the foundation for sophisticated agent behaviors and seamless component communication in ADK applications.