---
title: Get Started
description: Get up and running with ADK TypeScript in minutes
---

import { Cards, Card } from 'fumadocs-ui/components/card';
import { Callout } from 'fumadocs-ui/components/callout';
import { Steps } from 'fumadocs-ui/components/steps';

<Callout type="info" title="New to AI Agents?">
AI agents are autonomous programs that can understand instructions, use tools, and complete complex tasks. With ADK TypeScript, you can build agents that search the web, process files, interact with APIs, and coordinate with other agents.
</Callout>

## Quick Start Path

Follow this path to get from zero to your first working agent:

<Steps>

### Install ADK TypeScript

Set up your development environment and install the package.

[→ Installation Guide](/docs/framework/get-started/installation)

### Build Your First Agent

Create a simple agent that answers questions using Gemini models.

[→ Quickstart Tutorial](/docs/framework/get-started/quickstart)

### Understand the Architecture

Learn about the core concepts and components.

[→ About ADK TypeScript](/docs/framework)

</Steps>

## Choose Your Learning Path

<Cards>
  <Card
    title="🚀 I want to build quickly"
    description="Jump straight into code with minimal setup"
  >
    Start with the **[Quickstart](/docs/framework/get-started/quickstart)** - you'll have a working agent in 5 minutes.

    Perfect for: Prototyping, proof of concepts, getting a feel for the framework.
  </Card>

  <Card
    title="📚 I want to understand first"
    description="Learn the concepts before diving into code"
  >
    Read **[About ADK TypeScript](/docs/framework)** to understand the design philosophy.

    Perfect for: Planning production systems, understanding best practices.
  </Card>

  <Card
    title="🎯 I have a specific use case"
    description="I know what I want to build"
  >
    Browse our **<a href="https://github.com/IQAIcom/adk-ts/tree/main/apps/examples" target="_blank" rel="noopener noreferrer">Examples</a>** to find patterns that match your needs.

    Perfect for: Specific projects, learning by example, copying working code.
  </Card>
</Cards>

## What You Can Build

ADK TypeScript enables a wide range of AI agent applications:

### Simple Agents

```typescript
// Question answering agent
const response = await AgentBuilder
  .withModel("gemini-2.5-flash")
  .ask("Explain quantum computing in simple terms");
```

**Use cases**: Q&A bots, content generation, text analysis

### Tool-Enhanced Agents

```typescript
// Research agent with web search
const agent = new LlmAgent({
  name: "researcher",
  model: "gemini-2.5-flash",
  tools: [new GoogleSearch(), new FileOperationsTool()],
  instruction: "Research topics thoroughly and cite sources"
});
```

**Use cases**: Research assistants, data analysis, automated reporting

### Multi-Agent Workflows

```typescript
// Workflow: Research → Analyze → Summarize
const workflow = await AgentBuilder
  .create("content_pipeline")
  .asSequential([researchAgent, analysisAgent, summaryAgent])
  .build();
```

**Use cases**: Content pipelines, complex analysis, quality assurance

### Interactive Applications

```typescript
// Persistent chat with memory
const { runner } = await AgentBuilder
  .create("chat_assistant")
  .withModel("gemini-2.5-flash")
  .withSession(sessionService, userId, "chat-app")
  .build();
```

**Use cases**: Chatbots, virtual assistants, customer support

## Features at a Glance

<Cards>
  <Card
    title="🔀 Multiple Agent Types"
    description="LLM agents, Sequential workflows, Parallel processing, Loop-based iteration"
  />

  <Card
    title="🛠️ Rich Tool Ecosystem"
    description="Google Search, File operations, HTTP requests, Custom functions, MCP tools"
  />

  <Card
    title="💾 Session Management"
    description="In-memory sessions, Database persistence, Context preservation, Memory services"
  />

  <Card
    title="🎯 TypeScript Native"
    description="Full type safety, IntelliSense support, Modern async/await, ESM modules"
  />

  <Card
    title="🚀 Simple to Deploy"
    description="Node.js compatible, Docker ready, Cloud native, Serverless friendly"
  />

  <Card
    title="📊 Built-in Observability"
    description="Structured logging, Event streaming, Performance metrics, Error tracking"
  />
</Cards>

## Real Examples

All our documentation uses real, working TypeScript examples from our test suite. You can copy and run any code you see.

<Callout type="info" title="Working Examples">
Every code sample in our documentation comes from our [examples](https://github.com/IQAICOM/adk-ts/tree/main/apps/examples/src) directory - real, tested, working code that you can run immediately.
</Callout>

## Community & Support

Join our community for help, discussions, and updates:

<Cards>
  <Card
    title="💻 GitHub"
    description="Source code, issues, and contributions"
    href="https://github.com/IQAIcom/adk-ts"
    target="_blank"
    rel="noopener noreferrer"
  />
  <Card
    title="💬 Discussions"
    description="Ask questions, share ideas, and get help"
    href="https://github.com/IQAIcom/adk-ts/discussions"
    target="_blank"
    rel="noopener noreferrer"
  />
  {/* <Card
    title="Discord"
    description="Join our Discord server for real-time collaboration and support"
    href="https://discord.gg/your-discord-invite"
  />
  <Card
    title="🔬 Examples"
    description="Real working examples for common patterns"
    href="https://github.com/IQAIcom/adk-ts/tree/main/apps/examples"
  /> */}
</Cards>

## Next Steps

Ready to start building? Choose your path:

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
  <Card
    title="⚡ Quick Start"
    description="Build your first agent in 5 minutes"
    href="/docs/framework/get-started/quickstart"
  />

  <Card
    title="🏗️ Installation"
    description="Set up your development environment"
    href="/docs/framework/get-started/installation"
  />

  <Card
    title="🧠 Learn the Concepts"
    description="Understand the architecture"
    href="/docs/framework/get-started/about"
  />

  <Card
    title="🤖 Explore Agents"
    description="Learn about different agent types"
    href="/docs/framework/agents"
  />

  <Card
    title="🛠️ Use Tools"
    description="Add capabilities to your agents"
    href="/docs/framework/tools"
  />

  <Card
    title="📊 See Examples"
    description="Browse working code examples"
    href="https://github.com/IQAIcom/adk-ts/tree/main/apps/examples"
    target="_blank"
    rel="noopener noreferrer"
  />
</div>
