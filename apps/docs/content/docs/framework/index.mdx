---
title: Home
description: Build, deploy, and manage AI agents with TypeScript
icon: House
---

import { <PERSON>s, Card } from 'fumadocs-ui/components/card';
import { Callout } from 'fumadocs-ui/components/callout';

<Callout type="warn" title="Work in Progress">
This is a TypeScript port inspired by the original Python ADK. Features and stability are actively being developed.
</Callout>

## What is ADK for TypeScript?

Agent Development Kit (ADK) for TypeScript is a powerful, open-source framework for **building, orchestrating, and deploying AI agents**.

Inspired by the architectural principles of Google's Python ADK, our project is reimagined for the TypeScript ecosystem. We offer first-class support for Google Gemini models while providing the freedom to **integrate with any LLM, tool, or data source**. ADK makes it easy to start with simple agents and scales to support complex, multi-agent systems in any Node.js environment.

## Why ADK for TypeScript?

Our vision is to empower every developer to build intelligent agents with the same confidence as any modern software. We achieve this through three core pillars:

-   **Unparalleled Developer Experience:** We are TypeScript-first, providing full type-safety and autocompletion out of the box. Our intuitive `AgentBuilder` API lets you create powerful agents with minimal, readable code.
-   **Powerful & Flexible Architecture:** ADK is modular by design. Compose multiple agents, equip them with custom tools, and orchestrate complex workflows. Our provider-agnostic foundation means you're never locked into a single ecosystem.
-   **Production-Ready from Day One:** Go beyond prototypes. With built-in session management, persistent memory, and first-class OpenTelemetry support for observability, ADK provides the robust foundation needed for real-world applications.

## Core Features

<Cards>
  <Card
    title="🤖 Multi-Agent Systems"
    description="Orchestrate teams of specialized agents that collaborate to solve complex problems."
    href="/docs/framework/agents/multi-agents"
  />
  <Card
    title="🔀 Flexible Orchestration"
    description="Build complex workflows using sequential, parallel, or LLM-driven dynamic routing."
    href="/docs/framework/agents"
  />
  <Card
    title="💾 State & Memory"
    description="Enable persistent conversations with state management and context preservation across interactions."
    href="/docs/framework/sessions"
  />
  <Card
    title="🔄 Multi-LLM Support"
    description="Powered by Vercel AI SDK. Seamlessly use various models from Google, OpenAI, Anthropic, Mistral, and more."
    href="/docs/framework/agents/models"
  />
  <Card
    title="🛠️ Rich Tool Ecosystem"
    description="Equip agents with powerful tools via the Model Context Protocol (MCP)—connect to a wide range of MCP servers in the market or build your own."
    href="/docs/framework/tools"
  />
  <Card
    title="🧭 Built-in Tracing"
    description="Debug with confidence. First-class OpenTelemetry integration provides deep insights into your agent's execution."
    href="/docs/framework/advanced"
  />
  <Card
    title="📊 Built-in Evaluation"
    description="Systematically assess agent performance by testing final responses and execution trajectories."
    href="/docs/framework/evaluate"
  />
</Cards>

## What You Can Build

<Cards>
  <Card
    title="Sophisticated AI Assistants"
    description="Build chatbots that go beyond simple Q&A, with persistent memory and access to real-time information."
  />
  <Card
    title="Autonomous Task Agents"
    description="Create agents that can perform multi-step tasks, like analyzing data, booking appointments, or managing files."
  />
  <Card
    title="Collaborative Agent Workflows"
    description="Orchestrate multiple agents to solve complex problems, from automated research to code generation."
  />
  <Card
    title="AI-Powered Applications"
    description="Connect agents to your databases and APIs to create intelligent, interactive frontends for your services."
  />
</Cards>

## Design Philosophy

ADK is designed with the following principles:

-   **TypeScript-first:** Full type safety and a modern developer experience.
-   **Simple APIs:** Common patterns should be one-liners.
-   **Gradual Complexity:** Start simple and add power as you need it.
-   **Extensibility:** Every core component is designed to be replaced or extended.
-   **Production Ready:** Built-in observability, error handling, and scalability.
-   **Excellent Google Integration:** While provider-agnostic, we ensure seamless support for Gemini models and Google Cloud services.

---

## Learn More

Ready to dive in? Our documentation covers everything from your first agent to advanced deployment.

<Cards>
  <Card
    title="🏁 Get Started"
    description="Installation, quickstart, and your first agent"
    href="/docs/framework/get-started"
  />
  <Card
    title="📚 Agents Guide"
    description="Learn about LLM agents, workflow agents, and custom agents"
    href="/docs/framework/agents"
  />
  <Card
    title="🔧 Tools Reference"
    description="Built-in tools, custom functions, and third-party integrations"
    href="/docs/framework/tools"
  />
  <Card
    title="🎯 Advanced Concepts"
    description="Dive into sessions, memory, flows, and deployment strategies"
    href="/docs/framework/advanced"
  />
</Cards>

## Community & Support

Join our community for help, discussions, and updates:

<Cards>
  <Card
    title="💻 GitHub"
    description="Source code, issues, and contributions"
    href="https://github.com/IQAIcom/adk-ts"
    target="_blank"
    rel="noopener noreferrer"
  />
  <Card
    title="💬 Discussions"
    description="Ask questions, share ideas, and get help"
    href="https://github.com/IQAIcom/adk-ts/discussions"
    target="_blank"
    rel="noopener noreferrer"
  />
 <Card
    title="🔬 Examples"
    description="Real working examples for common patterns"
    href="https://github.com/IQAIcom/adk-ts/tree/main/apps/examples"
    target="_blank"
    rel="noopener noreferrer"
  />
</Cards>
