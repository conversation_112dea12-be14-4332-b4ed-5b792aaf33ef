---
title: Sessions & Memory
description: Manage conversational context, state, and long-term memory for intelligent agents
---

import { <PERSON><PERSON>, Card } from 'fumadocs-ui/components/card';
import { Callout } from 'fumadocs-ui/components/callout';

Meaningful multi-turn conversations require agents to understand and maintain context across interactions. Like humans, agents need to recall what's been said, remember user preferences, and access relevant knowledge to maintain continuity and avoid repetition. ADK TypeScript provides comprehensive context management through Sessions, State scoping, and Memory systems.

## Why Context Matters

Context is fundamental to intelligent conversation:

- **Continuity**: Maintain conversation flow across multiple turns and sessions
- **Personalization**: Remember user preferences, patterns, and past interactions
- **Efficiency**: Avoid repeating information or re-asking the same questions
- **Intelligence**: Build upon previous knowledge and successful interaction patterns

<Callout type="info" title="Conversation Intelligence">
Think of agent interactions as having distinct conversation threads that can draw upon long-term knowledge, personal preferences, and shared information across different scopes.
</Callout>

## Core Concepts

<Cards>
  <Card
    title="💬 Session"
    description="Current conversation thread with chronological message history and scoped state"
  />

  <Card
    title="🗂️ State"
    description="Scoped data management: session-specific, user-wide, app-global, and temporary"
  />

  <Card
    title="🧠 Memory"
    description="Searchable, cross-session knowledge base with keyword or semantic search"
  />
</Cards>

## Session: Current Conversation Thread

Sessions represent individual, ongoing interactions between users and your agent system, managing both event history and scoped state.

### Key Characteristics

- **Conversation Scope**: Contains the complete history of one interaction thread
- **Event Sequence**: Chronological sequence of messages, responses, and actions
- **Scoped State**: Organized data storage with automatic prefix handling (session, user:, app:, temp:)
- **Lifecycle Management**: Created, updated, and eventually completed or archived

### Implementation Options

**InMemorySessionService:**
```typescript
import { InMemorySessionService } from '@iqai/adk';

const sessionService = new InMemorySessionService();
// Fast development, no persistence
```

**DatabaseSessionService:**
```typescript
import { DatabaseSessionService } from '@iqai/adk';

const sessionService = new DatabaseSessionService({
  db: database, // Kysely instance
  skipTableCreation: false
});
// Persistent storage with SQLite, PostgreSQL, MySQL
```

**VertexAiSessionService:**
```typescript
import { VertexAiSessionService } from '@iqai/adk';

const sessionService = new VertexAiSessionService({
  project: 'your-project',
  location: 'us-central1',
  agentEngineId: 'your-agent-engine'
});
// Enterprise cloud scaling with Google Cloud
```

### Use Cases

- **Chat Applications**: Each user conversation as a separate session thread
- **Task Completion**: Multi-step processes that span several interactions
- **User Support**: Individual support tickets or help interactions
- **Collaborative Work**: Shared sessions for team-based agent interactions

## State: Conversation-Specific Data

State manages information relevant to current conversations while providing automatic scoping for different data lifecycles.

### State Scopes

Automatic data organization using prefix patterns:

- **Session State** (no prefix): Data specific to the current conversation
- **User State** (`user:` prefix): Data shared across all sessions for a specific user
- **App State** (`app:` prefix): Global application data shared across all users
- **Temporary State** (`temp:` prefix): Short-lived data that doesn't persist

### Practical Example

```typescript
// State automatically organized by scope
session.state = {
  'current_step': 'payment',           // Session-specific
  'user:preferred_language': 'es',     // User-specific across sessions
  'app:feature_flags': {...},          // App-wide settings
  'temp:api_response': {...}           // Temporary processing data
};

// Database implementations automatically separate:
// sessions table: { "current_step": "payment" }
// user_states table: { "preferred_language": "es" }
// app_states table: { "feature_flags": {...} }
// temp: discarded after processing
```

### Data Management

- **Event-Driven Updates**: State changes tracked through EventActions for auditability
- **Automatic Persistence**: Changes saved according to SessionService implementation
- **Prefix-Based Organization**: Automatic separation and merging by implementation
- **Delta Tracking**: Only changes stored for efficiency

### Common Use Cases

- **Shopping Cart**: Session items, user preferences, app-wide pricing
- **Form Data**: Multi-step form completion across message turns
- **User Preferences**: UI settings, language choices, notification preferences
- **Workflow Progress**: Current step in multi-stage processes with user context

## Memory: Long-Term Knowledge Store

Memory represents searchable information that spans multiple sessions, creating a knowledge base for enhanced conversations.

### Memory Types

- **Conversation Archives**: Historical conversations and their successful outcomes
- **Knowledge Base**: Structured information, documentation, and learned patterns
- **User Profiles**: Long-term user behavior patterns and preferences
- **External Data**: Integration with external knowledge sources and APIs

### Implementation Options

**InMemoryMemoryService:**
```typescript
import { InMemoryMemoryService } from '@iqai/adk';

const memoryService = new InMemoryMemoryService();
// Keyword-based search, development use
```

**VertexAiRagMemoryService:**
```typescript
import { VertexAiRagMemoryService } from '@iqai/adk';

const memoryService = new VertexAiRagMemoryService(
  'projects/project/locations/us-central1/ragCorpora/corpus-id',
  10,   // top-k results
  0.5   // similarity threshold
);
// Semantic search with vector embeddings
```

### Search Capabilities

- **Keyword Search**: Traditional text-based search with word matching
- **Semantic Search**: AI-powered understanding of meaning and context
- **Contextual Retrieval**: Context-aware information retrieval for relevant results
- **Cross-Session Learning**: Build knowledge from successful interactions

### Integration Patterns

- **RAG (Retrieval-Augmented Generation)**: Enhance responses with retrieved information
- **Session Summaries**: Automatically summarize and store completed sessions
- **Learning**: Build knowledge from successful interaction patterns
- **Personalization**: Customize responses based on historical preferences and context

## Service Architecture

ADK TypeScript provides services to manage these concepts effectively with different implementation options.

### SessionService: Conversation Management

Handles the complete lifecycle of conversation threads:

- **Creation**: Start new sessions with proper state initialization and scoping
- **Retrieval**: Access session history and current state with filtering options
- **Updates**: Append events and modify session state atomically
- **Completion**: Properly close and archive completed sessions

### MemoryService: Knowledge Management

Manages long-term information storage and retrieval:

- **Ingestion**: Store information from completed sessions with content filtering
- **Search**: Query stored knowledge with keyword or semantic search methods
- **Organization**: Structure information for efficient retrieval and relevance
- **Maintenance**: Manage memory size, quality, and relevance over time

## Implementation Recommendations

<Cards>
  <Card
    title="🚀 Development"
    description="InMemory services for fast iteration and testing"
  />

  <Card
    title="🏢 Production"
    description="Database services for reliable persistence and control"
  />

  <Card
    title="☁️ Enterprise"
    description="Cloud services for global scale and advanced AI capabilities"
  />
</Cards>

### Development Setup

**Quick Start:**
```typescript
import { InMemorySessionService, InMemoryMemoryService } from '@iqai/adk';

const sessionService = new InMemorySessionService();
const memoryService = new InMemoryMemoryService();

// Fast development iteration with no persistence requirements
```

### Production Setup

**Database-Backed:**
```typescript
import { DatabaseSessionService, VertexAiRagMemoryService } from '@iqai/adk';

const sessionService = new DatabaseSessionService({ db: database });
const memoryService = new VertexAiRagMemoryService(ragCorpus);

// Persistent sessions with semantic memory
```

### Enterprise Setup

**Cloud-Native:**
```typescript
import { VertexAiSessionService, VertexAiRagMemoryService } from '@iqai/adk';

const sessionService = new VertexAiSessionService({
  project: 'enterprise-project',
  location: 'us-central1'
});
const memoryService = new VertexAiRagMemoryService(ragCorpus);

// Fully managed, enterprise-scale infrastructure
```

<Callout type="warn" title="Data Persistence">
In-memory implementations lose all data when your application restarts. Use persistent implementations for production environments where data continuity is important.
</Callout>

## Context Management Patterns

### Hierarchical Context

Organize context in layers for optimal information flow:

1. **Immediate Context**: Current message and recent history within the session
2. **Session Context**: Full conversation history and scoped state
3. **User Context**: Cross-session user information and preferences
4. **Domain Context**: Relevant knowledge base information and patterns

### Context Optimization

- **Relevance Filtering**: Include only relevant historical information for current context
- **Summarization**: Compress long conversations into key points and outcomes
- **Prioritization**: Weight recent information higher than historical data
- **Size Management**: Keep context within model limits while preserving key information

### Privacy and Security

- **Data Encryption**: Encrypt sensitive session and memory data at rest and in transit
- **Access Control**: Limit access to user-specific information with proper scoping
- **Retention Policies**: Automatically remove old or sensitive data according to policies
- **Compliance**: Meet regulatory requirements for data handling and privacy

## Best Practices

### Session Design

- **Clear Boundaries**: Define when sessions start and end based on user intent
- **Appropriate Scope**: Keep sessions focused on specific tasks or conversation topics
- **State Organization**: Use consistent naming and scoping patterns for state data
- **Cleanup Strategy**: Properly close and archive completed sessions

### Memory Management

- **Relevant Storage**: Store only useful information that improves future interactions
- **Efficient Retrieval**: Design search strategies for quick, relevant results
- **Quality Control**: Maintain high-quality, accurate information through filtering
- **Performance Optimization**: Optimize for fast search and retrieval under load

### Integration Strategy

- **Service Selection**: Choose appropriate implementations for your scale and requirements
- **Error Handling**: Implement robust error handling for storage and retrieval failures
- **Monitoring**: Track session and memory performance metrics and usage patterns
- **Testing**: Test context management with realistic conversation flows and edge cases

## Quick Start Example

```typescript
import {
  InMemorySessionService,
  InMemoryMemoryService,
  Event,
  EventActions
} from '@iqai/adk';

// Set up services
const sessionService = new InMemorySessionService();
const memoryService = new InMemoryMemoryService();

// Create session with scoped state
const session = await sessionService.createSession(
  'travel-app',
  'user123',
  {
    'current_flow': 'booking',
    'user:preferred_language': 'es',
    'app:booking_version': '2.1'
  }
);

// Add interaction with state update
const event = new Event({
  author: 'user',
  content: { parts: [{ text: 'I want to book a hotel in Paris' }] },
  actions: new EventActions({
    stateDelta: {
      'destination': 'paris',
      'user:recent_searches': ['paris', 'hotels'],
      'temp:search_timestamp': Date.now()
    }
  }),
  timestamp: Date.now() / 1000
});

await sessionService.appendEvent(session, event);

// Archive completed session to memory
await memoryService.addSessionToMemory(session);

// Search memory for relevant information
const memories = await memoryService.searchMemory({
  appName: 'travel-app',
  userId: 'user123',
  query: 'hotel preferences paris'
});

console.log(`Found ${memories.memories.length} relevant memories`);
```

## Related Topics

<Cards>
  <Card
    title="🤖 Agents"
    description="Learn how agents use sessions and memory for context"
    href="/docs/framework/agents"
  />

  <Card
    title="🔧 Tools"
    description="How tools interact with session state and memory"
    href="/docs/framework/tools"
  />

  <Card
    title="📋 Callbacks"
    description="Handle session and memory events with callbacks"
    href="/docs/framework/callbacks"
  />

  <Card
    title="📊 Evaluation"
    description="Evaluate agent performance across sessions and memory"
    href="/docs/framework/evaluation"
  />
</Cards>

## Deep Dive Topics

Ready to explore specific aspects of session and memory management:

1. **[Session Management](/docs/sessions/session)** - Detailed session lifecycle, implementations, and advanced usage
2. **[State Management](/docs/sessions/state)** - Working with scoped state and data persistence patterns
3. **[Memory Systems](/docs/sessions/memory)** - Long-term memory implementations and search capabilities