---
title: MCP ABI
description: Smart contract ABI interactions for Ethereum-compatible blockchains
---

import { Callout } from 'fumadocs-ui/components/callout';
import { Tabs, Tab } from 'fumadocs-ui/components/tabs';

- **Package**: [`@iqai/mcp-abi`](https://www.npmjs.com/package/@iqai/mcp-abi)
- **Purpose**: Smart contract ABI interactions for Ethereum-compatible blockchains


## Usage with ADK TypeScript

<Tabs items={['Simple', 'Verbose', 'Claude Desktop']}>
  <Tab value="Simple">
    ```typescript
    import {Mcp<PERSON><PERSON>} from "@iqai/adk";

    const toolset = McpAbi({
      env: {
        WALLET_PRIVATE_KEY: process.env.WALLET_PRIVATE_KEY,
        CONTRACT_ABI: process.env.CONTRACT_ABI,
        CONTRACT_ADDRESS: process.env.CONTRACT_ADDRESS,
        CONTRACT_NAME: "ERC20",
        CHAIN_ID: "252",
        RPC_URL: "https://rpc.frax.com"
      },
    })

    const tools = await toolset.getTools()
    ```
  </Tab>

  <Tab value="Verbose">
    ```typescript
    import {McpToolset} from "@iqai/adk";

    const toolset = new McpToolset({
      name: "ABI MCP Client",
      description: "Client for smart contract ABI interactions",
      transport: {
        mode: "stdio",
        command: "pnpm",
        args: ["dlx", "@iqai/mcp-abi"],
        env: {
          WALLET_PRIVATE_KEY: process.env.WALLET_PRIVATE_KEY,
          CONTRACT_ABI: process.env.CONTRACT_ABI,
          CONTRACT_ADDRESS: process.env.CONTRACT_ADDRESS,
          CONTRACT_NAME: "ERC20",
          CHAIN_ID: "252",
          RPC_URL: "https://rpc.frax.com",
          PATH: process.env.PATH || "",
        },
      },
    })

    const tools = await toolset.getTools()
    ```
  </Tab>

  <Tab value="Claude Desktop">
    ```json
    {
      "mcpServers": {
        "smart-contract-abi": {
          "command": "pnpm",
          "args": ["dlx", "@iqai/mcp-abi"],
          "env": {
            "WALLET_PRIVATE_KEY": "your_wallet_private_key_here",
            "CONTRACT_ABI": "[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}]",
            "CONTRACT_ADDRESS": "******************************************",
            "CONTRACT_NAME": "ERC20",
            "CHAIN_ID": "252",
            "RPC_URL": "https://rpc.frax.com"
          }
        }
      }
    }
    ```
  </Tab>
</Tabs>

## Features

- Dynamically generates MCP tools based on contract ABI
- Supports both read (view/pure) and write functions
- Automatic tool naming with contract prefixes
- Compatible with any Ethereum-compatible blockchain
- Comprehensive error handling and transaction management

## Available Tools

The server automatically creates tools for each function in the provided ABI:

- **Read Functions**: Query contract state without transactions
  - Example: `ERC20_BALANCE_OF`, `ERC20_TOTAL_SUPPLY`, `ERC20_NAME`
- **Write Functions**: Execute state-changing transactions
  - Example: `ERC20_TRANSFER`, `ERC20_APPROVE`, `ERC20_MINT`

## Environment Variables

| Variable             | Description                         | Required                             |
| -------------------- | ----------------------------------- | ------------------------------------ |
| `WALLET_PRIVATE_KEY` | Private key for transaction signing | For write functions                  |
| `CONTRACT_ABI`       | JSON string of the contract's ABI   | Yes                                  |
| `CONTRACT_ADDRESS`   | Deployed contract address           | Yes                                  |
| `CONTRACT_NAME`      | Friendly name for tool prefixes     | Optional (defaults to "CONTRACT")    |
| `CHAIN_ID`           | Blockchain network chain ID         | Optional (defaults to Fraxtal - 252) |
| `RPC_URL`            | Custom RPC endpoint URL             | Optional                             |

## Usage Examples

### ERC20 Token Contract

- **Check balance**: `ERC20_BALANCE_OF` with address parameter
- **Transfer tokens**: `ERC20_TRANSFER` with recipient and amount
- **Check allowances**: `ERC20_ALLOWANCE` with owner and spender
- **Approve spending**: `ERC20_APPROVE` with spender and amount

### NFT Contract (ERC721)

- **Check ownership**: `NFT_OWNER_OF` with token ID
- **Transfer NFT**: `NFT_TRANSFER_FROM` with from, to, and token ID
- **Mint token**: `NFT_MINT` with recipient and metadata

## Response Examples

**Read Function Response**:
```
✅ Successfully called balanceOf
Result: "1000000000000000000"
```

**Write Function Response**:
```
✅ Successfully executed transfer
Transaction hash: 0x123abc...
You can view this transaction on the blockchain explorer.
```

## Error Handling

<Callout type="warn" title="Common Errors">
The server provides comprehensive error handling for various blockchain interaction scenarios.
</Callout>

- 🚨 **Invalid function arguments** - "❌ Error parsing arguments: [specific error]"
- 🔄 **Transaction failures** - "❌ Error with [function]: [error message]"
- 🔒 **Access control errors** - "❌ Error with [function]: execution reverted"
- 🌐 **Network errors** - "❌ Error with [function]: network connection failed"
- 💲 **Insufficient funds** - "❌ Error with [function]: insufficient funds for gas"
- 📄 **ABI parsing errors** - "❌ Invalid ABI format: [specific error]"
- 🏠 **Contract address errors** - "❌ Invalid contract address: [address]"