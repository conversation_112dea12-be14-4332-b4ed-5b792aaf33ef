---
title: MCP BAMM
description: Model Context Protocol Server for Borrow Automated Market Maker
---

import { Callout } from 'fumadocs-ui/components/callout';
import { Tabs, Tab } from 'fumadocs-ui/components/tabs';

- **Package**: [`@iqai/mcp-bamm`](https://www.npmjs.com/package/@iqai/mcp-bamm)
- **Purpose**: Interacting with Borrow Automated Market Maker (BAMM) contracts on the Fraxtal blockchain.

## Usage with ADK TypeScript

<Tabs items={['Simple', 'Verbose', 'Claude Desktop']}>
  <Tab value="Simple">
    ```typescript
    import {McpBamm} from "@iqai/adk";

    const toolset = McpBamm({
      env: {
        WALLET_PRIVATE_KEY: process.env.WALLET_PRIVATE_KEY,
      },
      })

    const tools = await toolset.getTools()
    ```
  </Tab>

  <Tab value="Verbose">
    ```typescript
    import {McpToolset} from "@iqai/adk";

    const toolset = new McpToolset({
      name: "BAMM MCP Client",
      description: "Client for BAMM DeFi operations",
      transport: {
        mode: "stdio",
        command: "pnpm",
        args: ["dlx", "@iqai/mcp-bamm"],
        env: {
          WALLET_PRIVATE_KEY: process.env.WALLET_PRIVATE_KEY,
          PATH: process.env.PATH || "",
        },
      },
    })

    const tools = await toolset.getTools()
    ```
  </Tab>

  <Tab value="Claude Desktop">
    ```json
    {
      "mcpServers": {
        "bamm-mcp-server": {
          "command": "pnpm",
          "args": ["dlx", "@iqai/mcp-bamm"],
          "env": {
            "WALLET_PRIVATE_KEY": "your_wallet_private_key_here"
          }
        }
      }
    }
    ```
  </Tab>
</Tabs>


## Features

- Allows MCP-compatible clients to manage BAMM positions, borrow against LP tokens, and perform other operations related to the BAMM protocol.
- Built using TypeScript and `fastmcp`.

## Available Tools

The server exposes the following tools that MCP clients can utilize:

### `ADD_COLLATERAL`
Add collateral to your BAMM position.

- Parameters: `bammAddress` (string), `amount` (string), `collateralToken` (string, optional), `collateralTokenSymbol` (string, optional)
- Requires `WALLET_PRIVATE_KEY` in the environment.

### `BORROW`
Borrow tokens from a BAMM position.

- Parameters: `bammAddress` (string), `amount` (string), `borrowToken` (string, optional), `borrowTokenSymbol` (string, optional)
- Requires `WALLET_PRIVATE_KEY` in the environment.

### `REPAY`
Repay borrowed tokens to a BAMM position.

- Parameters: `bammAddress` (string), `amount` (string), `borrowToken` (string, optional), `borrowTokenSymbol` (string, optional)
- Requires `WALLET_PRIVATE_KEY` in the environment.

### `LEND`
Lend Fraxswap LP tokens to a BAMM contract.

- Parameters: `bammAddress` (string), `amount` (string)
- Requires `WALLET_PRIVATE_KEY` in the environment.

### `WITHDRAW`
Withdraw LP tokens from a BAMM contract by redeeming BAMM tokens.

- Parameters: `bammAddress` (string), `amount` (string)
- Requires `WALLET_PRIVATE_KEY` in the environment.

### `REMOVE_COLLATERAL`
Remove collateral from your BAMM position.

- Parameters: `bammAddress` (string), `amount` (string), `collateralToken` (string, optional), `collateralTokenSymbol` (string, optional)
- Requires `WALLET_PRIVATE_KEY` in the environment.

### `GET_POSITIONS`
Get all your active BAMM positions.

- Requires `WALLET_PRIVATE_KEY` in the environment.

### `POOL_STATS`
Get statistics for all BAMM pools.

- Requires `WALLET_PRIVATE_KEY` in the environment.

## Environment Variables

<Callout type="warn" title="Security Note">
Handle the private key with extreme care. Ensure it is stored securely and only provided to trusted MCP client configurations.
</Callout>

- `WALLET_PRIVATE_KEY`: (Required for all blockchain operations)
  - The private key of the wallet to be used for interacting with BAMM contracts (signing transactions for lending, borrowing, etc.).


## Usage Examples

```javascript
// First, ensure the WALLET_PRIVATE_KEY environment variable is set on the server

// Add collateral to a BAMM position
await client.runTool("ADD_COLLATERAL", {
  bammAddress: "******************************************",
  amount: "100",
  collateralTokenSymbol: "FRAX",
});

// Get all your positions
await client.runTool("GET_POSITIONS", {});
```

## Response Examples

```json
{ "txHash": "0x..." }
```

```
📊 *Your Active BAMM Positions*

**💰 BAMM Position**
- bamm: 0x...
- Pair: 0x...
- FRAX: 100
- USDC: 200
- rented: 0
```

## Error Handling

```
❌ Failed to retrieve positions: Failed to fetch pool details: Not Found
Error: Pool stats not available
Error: Remove collateral amount must be greater than 0
Error: Withdraw amount must be greater than 0
```