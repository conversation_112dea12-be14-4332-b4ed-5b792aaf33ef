import { docs } from '@/.source';
import { loader } from 'fumadocs-core/source';
import { icons, HomeIcon } from 'lucide-react';
import { createElement } from 'react';

// See https://fumadocs.vercel.app/docs/headless/source-api for more info
export const source = loader({
  // it assigns a URL to your pages
  baseUrl: '/docs',
  source: docs.toFumadocsSource(),

  // Icon support for navigation
  icon(icon) {
    if (!icon) {
      return;
    }
    if (icon in icons) return createElement(icons[icon as keyof typeof icons]);
    else console.log('icon not found', icon);
  },
});
