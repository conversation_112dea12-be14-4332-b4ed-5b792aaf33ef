{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dedent": "^1.6.0", "fumadocs-core": "15.6.1", "fumadocs-mdx": "11.6.10", "fumadocs-ui": "15.6.1", "lucide-react": "^0.525.0", "next": "15.3.4", "posthog-js": "^1.257.0", "posthog-node": "^5.5.1", "react": "^19.1.0", "react-dom": "^19.1.0", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-mdx": "^3.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/mdx": "^2.0.13", "@types/node": "24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3"}}