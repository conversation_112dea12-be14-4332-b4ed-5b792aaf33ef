{"name": "adk-simple-agent", "version": "0.0.1", "description": "adk-simple-agent is a starter project for creating simple adk agent", "main": "dist/index.js", "private": true, "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "pnpm clean"}, "repository": {"type": "git", "url": "git+https://github.com/IQAICOM/adk-agent-starter.git"}, "keywords": ["ai", "agent", "mcp", "adk-ts", "IQAI"], "author": "IQAI", "license": "MIT", "bugs": {"url": "https://github.com/IQAICOM/adk-agent-starter/issues"}, "homepage": "https://github.com/IQAICOM/adk-agent-starter#readme", "dependencies": {"@iqai/adk": "^0.1.8", "dotenv": "^17.2.0", "zod": "^4.0.5"}, "devDependencies": {"@types/node": "^22.16.0", "rimraf": "^6.0.1", "tsx": "^4.20.3", "typescript": "^5.8.3"}}