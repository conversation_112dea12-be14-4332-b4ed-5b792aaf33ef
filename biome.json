{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": ["node_modules/**", "coverage/**", "dist/**", "*.lock", "*.log", "docs/**"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off"}, "complexity": {"noForEach": "off", "noStaticOnlyClass": "off"}, "style": {"noNonNullAssertion": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}, "overrides": [{"include": ["**/*.test.ts", "**/*.spec.ts"], "linter": {"rules": {"suspicious": {"noPrototypeBuiltins": "off", "noExplicitAny": "off"}, "complexity": {"useLiteralKeys": "off"}}}}]}