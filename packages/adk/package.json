{"name": "@iqai/adk", "version": "0.1.10", "description": "Agent Development Kit for TypeScript with multi-provider LLM support", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "repository": {"type": "git", "url": "https://github.com/IQAIcom/adk-ts.git"}, "keywords": ["ai", "llm", "agent", "openai", "anthropic", "gemini", "typescript"], "author": "IQAI", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@electric-sql/pglite": "^0.3.2", "@google-cloud/storage": "^7.16.0", "@google-cloud/vertexai": "^0.5.0", "@google/genai": "^1.6.0", "@modelcontextprotocol/sdk": "^1.11.1", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.60.1", "@opentelemetry/exporter-trace-otlp-http": "^0.202.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-node": "^0.202.0", "@opentelemetry/sdk-trace-base": "^2.0.1", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.34.0", "ai": "^4.3.16", "axios": "^1.6.2", "chalk": "^5.4.1", "dedent": "^1.6.0", "dockerode": "^4.0.7", "dotenv": "^16.4.7", "drizzle-orm": "^0.43.1", "kysely": "^0.28.2", "openai": "^4.93.0", "uuid": "^11.1.0", "zod": "^3.25.67"}, "peerDependencies": {"better-sqlite3": "^11.10.0", "mysql2": "^3.14.1", "pg": "^8.16.0"}, "peerDependenciesMeta": {"better-sqlite3": {"optional": true}, "mysql2": {"optional": true}, "pg": {"optional": true}}, "devDependencies": {"@iqai/tsconfig": "workspace:*", "@types/better-sqlite3": "^7.6.13", "@types/dockerode": "^3.3.42", "@types/node": "^20.17.30", "better-sqlite3": "^11.10.0", "mysql2": "^3.14.1", "pg": "^8.16.0", "tsup": "^8.4.0", "typescript": "^5.3.2", "vitest": "^3.1.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=22.0"}, "files": ["dist", "!**/*.test.*", "!**/*.json", "CHANGELOG.md", "LICENSE", "README.md"], "publishConfig": {"access": "public"}}