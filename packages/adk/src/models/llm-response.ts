import type {
	Content,
	GenerateContentResponseUsageMetadata,
	GroundingMetadata,
} from "@google/genai";

/**
 * Candidate response from the model.
 */
interface Candidate {
	content?: Content;
	groundingMetadata?: GroundingMetadata;
	finishReason?: string;
	finishMessage?: string;
}

/**
 * Prompt feedback in case of blocked or failed prompt.
 */
interface PromptFeedback {
	blockReason?: string;
	blockReasonMessage?: string;
}

/**
 * The response from the model generation API.
 */
interface GenerateContentResponse {
	candidates?: Candidate[];
	usageMetadata?: GenerateContentResponseUsageMetadata;
	promptFeedback?: PromptFeedback;
}

/**
 * Response from a language model.
 */
export class LlmResponse {
	/**
	 * Unique identifier for the response.
	 */
	id?: string;

	/**
	 * The content generated by the model.
	 */
	content?: Content;

	/**
	 * The grounding metadata of the response.
	 */
	groundingMetadata?: GroundingMetadata;

	/**
	 * Indicates whether the text content is part of an unfinished text stream.
	 */
	partial?: boolean;

	/**
	 * Indicates whether the response from the model is complete.
	 */
	turnComplete?: boolean;

	/**
	 * Error code if the response is an error.
	 */
	errorCode?: string;

	/**
	 * Error message if the response is an error.
	 */
	errorMessage?: string;

	/**
	 * Flag indicating that LLM was interrupted when generating the content.
	 */
	interrupted?: boolean;

	/**
	 * The custom metadata of the LlmResponse.
	 */
	customMetadata?: Record<string, any>;

	/**
	 * The usage metadata of the LlmResponse.
	 */
	usageMetadata?: GenerateContentResponseUsageMetadata;

	/**
	 * Index of the candidate response.
	 */
	candidateIndex?: number;

	/**
	 * Reason why the model finished generating.
	 */
	finishReason?: string;

	/**
	 * Error object if the response is an error.
	 */
	error?: Error;

	/**
	 * Creates a new LlmResponse.
	 */
	constructor(data: Partial<LlmResponse> = {}) {
		Object.assign(this, data);
	}

	/**
	 * Creates an LlmResponse from a GenerateContentResponse.
	 *
	 * @param generateContentResponse The GenerateContentResponse to create the LlmResponse from.
	 * @returns The LlmResponse.
	 */
	static create(generateContentResponse: GenerateContentResponse): LlmResponse {
		const usageMetadata = generateContentResponse.usageMetadata;
		if (
			generateContentResponse.candidates &&
			generateContentResponse.candidates.length > 0
		) {
			const candidate = generateContentResponse.candidates[0];
			if (candidate.content && (candidate.content as any).parts) {
				return new LlmResponse({
					content: candidate.content,
					groundingMetadata: candidate.groundingMetadata,
					usageMetadata,
				});
			}
			return new LlmResponse({
				errorCode: candidate.finishReason,
				errorMessage: candidate.finishMessage,
				usageMetadata,
			});
		}
		if (generateContentResponse.promptFeedback) {
			const promptFeedback = generateContentResponse.promptFeedback;
			return new LlmResponse({
				errorCode: promptFeedback.blockReason,
				errorMessage: promptFeedback.blockReasonMessage,
				usageMetadata,
			});
		}
		return new LlmResponse({
			errorCode: "UNKNOWN_ERROR",
			errorMessage: "Unknown error.",
			usageMetadata,
		});
	}

	/**
	 * Creates an LlmResponse from an error.
	 *
	 * @param error The error object or message.
	 * @param options Additional options for the error response.
	 * @param options.errorCode A specific error code for the response.
	 * @param options.model The model that was being used when the error occurred.
	 * @returns The LlmResponse.
	 */
	static fromError(
		error: unknown,
		options: { errorCode?: string; model?: string } = {},
	): LlmResponse {
		const errorMessage = error instanceof Error ? error.message : String(error);
		const errorCode = options.errorCode || "UNKNOWN_ERROR";

		return new LlmResponse({
			errorCode,
			errorMessage: `LLM call failed for model ${
				options.model || "unknown"
			}: ${errorMessage}`,
			content: {
				role: "model",
				parts: [{ text: `Error: ${errorMessage}` }],
			},
			finishReason: "STOP",
			error: error instanceof Error ? error : new Error(errorMessage),
		});
	}
}
