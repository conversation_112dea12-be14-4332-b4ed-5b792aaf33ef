# create-adk-project

## 1.0.3

### Patch Changes

- c3f0001: Adds more starter templates

## 1.0.2

### Patch Changes

- 06ecd66: Fixes cli next steps numbering

## 1.0.1

### Patch Changes

- 058e7de: Adds minor changes to cli output after project initalization

## 1.0.0

### Major Changes

- 77be658: Add create-adk-project CLI tool for scaffolding ADK projects

  - Interactive CLI using @clack/prompts for beautiful user experience
  - Support for Hono, Express, and Next.js starter templates
  - Automatic dependency installation option
  - Uses giget for cloning templates from GitHub
  - Includes starter templates with ADK integration and TypeScript support
