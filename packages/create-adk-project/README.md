<div align="center">

<img src="https://files.catbox.moe/vumztw.png" alt="ADK TypeScript Logo" width="100" />

<br/>

# create-adk-project

**CLI tool to create ADK TypeScript agent projects with a complete starter template**

*Interactive Setup • Project Scaffolding • Ready-to-Run Examples*

---

</div>

## Usage

```bash
npx create-adk-project
```

Or install globally:

```bash
npm install -g create-adk-project
create-adk-project
```

## Interactive Setup

The CLI will guide you through:

1. **Project Name** - Choose your project directory name
2. **Template Selection** - Downloads the official ADK agent starter template
3. **Dependency Installation** - Choose from available package managers (npm, pnpm, yarn, bun)
4. **Environment Setup** - Creates `.env` template file

## Features

- 🚀 Interactive prompts powered by clack
- 🎨 Beautiful CLI interface with ASCII art
- 📦 Smart package manager detection (npm, pnpm, yarn, bun)
- ⚡ Fast project scaffolding from official starter template
- 🔧 Automatic dependency installation
- 📋 Ready-to-run example code from starter template

## Template Source

The starter template is maintained at: [adk-agent-starter](https://github.com/IQAIcom/adk-agent-starter)

## Requirements

- Node.js v22.0 or higher
- npm, yarn, or pnpm

## License

MIT