{"name": "create-adk-project", "version": "1.0.3", "description": "CLI tool to create ADK TypeScript projects with different frameworks", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "bin": {"create-adk-project": "dist/index.js"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest run"}, "repository": {"type": "git", "url": "https://github.com/IQAIcom/adk-ts.git"}, "keywords": ["ai", "llm", "agent", "cli", "typescript", "create-project"], "author": "IQAI", "license": "MIT", "dependencies": {"@clack/prompts": "^0.7.0", "giget": "^1.2.3", "chalk": "^5.4.1", "dedent": "^1.6.0"}, "devDependencies": {"@iqai/tsconfig": "workspace:*", "@types/node": "^20.17.30", "tsup": "^8.4.0", "typescript": "^5.3.2", "vitest": "^3.1.3"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=22.0"}, "files": ["dist", "templates", "!**/*.test.*", "!**/*.json", "CHANGELOG.md", "LICENSE", "README.md"], "publishConfig": {"access": "public"}}