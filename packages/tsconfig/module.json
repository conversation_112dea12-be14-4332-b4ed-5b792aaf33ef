{"compilerOptions": {"target": "ESNext", "module": "ESNext", "lib": ["ESNext", "dom"], "moduleResolution": "<PERSON><PERSON><PERSON>", "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": false, "allowImportingTsExtensions": true, "declaration": true, "emitDeclarationOnly": true, "resolveJsonModule": true, "noImplicitAny": false, "allowJs": true, "checkJs": false, "noEmitOnError": false, "moduleDetection": "force", "allowArbitraryExtensions": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "src/**/*.d.ts", "types/**/*.test.ts"]}