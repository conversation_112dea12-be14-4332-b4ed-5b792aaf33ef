{"$schema": "https://json.schemastore.org/tsconfig", "extends": "./base.json", "compilerOptions": {"allowJs": true, "declaration": false, "declarationMap": false, "incremental": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noEmit": true, "resolveJsonModule": true, "target": "es5"}, "include": ["src", "next-env.d.ts"], "exclude": ["node_modules"]}